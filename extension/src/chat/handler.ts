import { db } from '~/storages/indexdb';
import {
  AddMemoryResponse,
  APIClient,
  Message,
  SaveMessageResponseV2,
  Task,
  ChatOptions,
  Agent,
  ChatStatus,
} from '@the-agent/shared';
import { ApiKey } from '~/types';
import { createApiClient } from '~/services/api/client';
import { createBrowserAgent, createTaskBrowserAgent } from '~/agents/browser';
import { GlobalContext } from '~/types/task';
import * as runner from '~/agents/runner';
import OpenAI from 'openai';
import { createForeachAgent } from '~/agents/foreach';
import { createPlannerAgent } from '~/agents/planner';
import { createDispatcherAgent } from '~/agents/dispatcher';
import { initTaskState } from '~/agents/runner';

export interface ChatHandlerOptions {
  apiKey: ApiKey | null;
  currentConversationId: number;
  workflowMode: boolean;
  onStatusChange: (status: ChatStatus) => void;
  onError: (error: unknown) => void;
  onMessageUpdate: (message: Message) => void;
}

export class ChatHandler {
  private apiClient: APIClient | null = null;
  private options: ChatHandlerOptions;
  private agents: Agent[] = [];
  private globalContext: GlobalContext | null = null;

  constructor(options: ChatHandlerOptions) {
    this.options = options;
  }

  async handleSubmit(prompt: string) {
    if (!prompt || !this.options.apiKey?.enabled) {
      return;
    }
    this.options.onStatusChange?.('running');
    if (!this.apiClient) {
      this.apiClient = await createApiClient(this.options.apiKey);
    }

    try {
      const model = await db.getSelectModel();
      const llmClient = new OpenAI({
        baseURL: model.apiUrl,
        apiKey: model.apiKey,
        dangerouslyAllowBrowser: true,
      });
      const chatOptions = this.buildChatOptions();
      if (this.options.workflowMode) {
        const workflowId = 'workflow_' + Date.now();
        const task: Task = {
          id: 'root',
          goal: prompt,
          output: '',
        };
        const c: GlobalContext = {
          workflowId,
          processing: [],
          processed: [],
          chatOptions,
          tasks: {},
          agents: {},
          aborted: false,
        };
        const browser = createTaskBrowserAgent(model.name, llmClient, this.apiClient, c);
        const planner = createPlannerAgent(model.name, llmClient, c);
        c.agents.dispatcher = createDispatcherAgent(model.name, llmClient, browser, planner, c);
        c.agents.foreach = createForeachAgent(model.name, llmClient, c);

        // Store references for abort functionality
        this.globalContext = c;

        const taskWithState = initTaskState(c, task);
        this.agents = [browser, planner, c.agents.dispatcher, c.agents.foreach];
        await runner.run(taskWithState, c);
      } else {
        if (this.agents.length > 0) {
          console.error('Agent is still running');
          throw new Error('Agent is still running');
        }

        const userMessage: Message = {
          id: Date.now(),
          conversation_id: chatOptions.conversationId,
          role: 'user',
          content: prompt,
          status: 'pending',
          actor: 'user',
        };
        const agent = createBrowserAgent(model.name, llmClient, this.apiClient);
        this.agents = [agent];
        await agent.run(userMessage, { chatOptions });
      }
    } catch (error: unknown) {
      this.options.onError(error);
    } finally {
      this.abort();
      this.agents = [];

      // Clean up workflow mode references
      this.globalContext = null;

      this.options.onStatusChange?.('idle');
    }
  }

  private buildChatOptions(): ChatOptions {
    return {
      conversationId: this.options.currentConversationId,
      onMessageUpdate: async (message: Message) => {
        await db.saveMessage(message);
      },
      onMessageComplete: async (message: Message) => {
        if (message.status !== 'error') {
          message.status = 'completed';
        }
        await db.saveMessage(message);
        await saveMessageRemote(this.apiClient!, message);
      },
      onStatusChange: async (status: ChatStatus) => {
        this.options.onStatusChange(status);
      },
    };
  }

  abort() {
    // Abort single agent mode
    this.agents.forEach(agent => agent.abort());

    // Abort workflow mode agents and set abort flag
    if (this.globalContext) {
      this.globalContext.aborted = true;
    }
  }
}

const saveMessageRemote = async (
  apiClient: APIClient,
  message: Message
): Promise<SaveMessageResponseV2> => {
  const result = await apiClient.saveMessageV2({ message });
  if (message.content && (message.role === 'user' || message.role === 'assistant')) {
    apiClient
      .addMemory({ messages: [message] })
      .then((result: AddMemoryResponse) => {
        console.log(
          `addMemory done for message ${message.id}: ${(result as any)?.results?.length ?? 0} memories added`
        );
      })
      .catch(error => {
        console.error(`add memory failed for message ${message.id}:`, error);
      }); // do not wait for memory adding, it may take a lot of time
  }
  return result;
};
